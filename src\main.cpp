/*
 * ESP32 Hardware Diagnostic Program
 *
 * This program performs comprehensive hardware diagnostics for the ESP32 soil moisture system.
 * Use this to identify and fix hardware issues before running the main monitoring program.
 *
 * Features:
 * - Board identification and capability analysis
 * - Pin assignment validation and recommendations
 * - Individual component testing (moisture sensor, DHT22, relay)
 * - Interactive testing mode
 * - Safe pin recommendations
 *
 * Author: ESP32 Soil Moisture Project
 * Date: 2025
 */

#include <Arduino.h>
#include "hardware_diagnostics.h"

// Function declarations
void printDiagnosticSummary(SystemDiagnostics results);
void printHelp();

// Create diagnostics instance
HardwareDiagnostics diagnostics;

void setup() {
  Serial.begin(115200);
  delay(2000); // Allow serial monitor to connect

  // Print welcome message
  Serial.println("╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║              ESP32 HARDWARE DIAGNOSTICS                  ║");
  Serial.println("║                                                           ║");
  Serial.println("║  This program will help identify and fix hardware        ║");
  Serial.println("║  issues with your ESP32 soil moisture monitoring system  ║");
  Serial.println("║                                                           ║");
  Serial.println("║  The diagnostic will:                                     ║");
  Serial.println("║  • Identify your ESP32 board type and capabilities       ║");
  Serial.println("║  • Validate current pin assignments                      ║");
  Serial.println("║  • Test each hardware component individually             ║");
  Serial.println("║  • Provide recommendations for fixing issues             ║");
  Serial.println("║                                                           ║");
  Serial.println("╚═══════════════════════════════════════════════════════════╝");
  Serial.println();

  // Initialize diagnostics
  diagnostics.initialize();
  Serial.println();

  // Run full diagnostics
  Serial.println("🚀 Starting Comprehensive Hardware Diagnostics...");
  Serial.println();

  SystemDiagnostics results = diagnostics.runFullDiagnostics();

  // Print summary
  printDiagnosticSummary(results);

  // Provide recommendations
  Serial.println("💡 RECOMMENDATIONS:");
  diagnostics.validateCurrentPinAssignments();
  diagnostics.recommendSafePins();

  // Offer interactive mode
  Serial.println("🎮 INTERACTIVE MODE AVAILABLE:");
  Serial.println("Type 'interactive' to enter interactive testing mode");
  Serial.println("Type 'scan' to scan all pins");
  Serial.println("Type 'restart' to run diagnostics again");
  Serial.println();
}

void loop() {
  // Check for user input
  if (Serial.available()) {
    String command = Serial.readStringUntil('\n');
    command.trim();
    command.toLowerCase();

    if (command == "interactive") {
      diagnostics.runInteractiveTest();
    } else if (command == "scan") {
      Serial.println("🔍 Scanning all pins...");
      diagnostics.scanAllPins();
    } else if (command == "restart") {
      Serial.println("🔄 Restarting diagnostics...");
      Serial.println();
      SystemDiagnostics results = diagnostics.runFullDiagnostics();
      printDiagnosticSummary(results);
    } else if (command == "help") {
      printHelp();
    } else if (command.length() > 0) {
      Serial.println("Unknown command. Type 'help' for available commands.");
    }
  }

  delay(100);
}

void printDiagnosticSummary(SystemDiagnostics results) {
  Serial.println("╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║                    DIAGNOSTIC SUMMARY                    ║");
  Serial.println("╠═══════════════════════════════════════════════════════════╣");

  // Board info
  Serial.printf("║ Board: %-50s ║\n", results.boardType.c_str());
  Serial.printf("║ Free Heap: %-43d KB ║\n", results.freeHeap / 1024);
  Serial.printf("║ Flash Size: %-42d MB ║\n", results.flashSize);
  Serial.println("║                                                           ║");

  // Component status
  Serial.printf("║ Moisture Sensor: %-40s ║\n",
                results.moistureSensor.isWorking ? "✓ WORKING" : "❌ FAILED");
  Serial.printf("║ DHT22 Sensor: %-43s ║\n",
                results.dhtSensor.isWorking ? "✓ WORKING" : "❌ FAILED");
  Serial.printf("║ Relay Module: %-43s ║\n",
                results.relayModule.isWorking ? "✓ WORKING" : "❌ FAILED");
  Serial.println("║                                                           ║");

  // Pin warnings
  bool hasWarnings = false;
  if (results.moisturePin.warnings.length() > 0) {
    Serial.printf("║ ⚠ Moisture Pin (GPIO%d): %s\n", results.moisturePin.pin,
                  results.moisturePin.warnings.c_str());
    hasWarnings = true;
  }
  if (results.dhtPin.warnings.length() > 0) {
    Serial.printf("║ ⚠ DHT22 Pin (GPIO%d): %s\n", results.dhtPin.pin,
                  results.dhtPin.warnings.c_str());
    hasWarnings = true;
  }
  if (results.relayPin.warnings.length() > 0) {
    Serial.printf("║ ⚠ Relay Pin (GPIO%d): %s\n", results.relayPin.pin,
                  results.relayPin.warnings.c_str());
    hasWarnings = true;
  }

  if (!hasWarnings) {
    Serial.println("║ Pin Assignments: ✓ NO WARNINGS                          ║");
  }

  Serial.println("╚═══════════════════════════════════════════════════════════╝");
  Serial.println();
}

void printHelp() {
  Serial.println("╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║                      HELP - COMMANDS                     ║");
  Serial.println("╠═══════════════════════════════════════════════════════════╣");
  Serial.println("║ interactive  - Enter interactive testing mode            ║");
  Serial.println("║ scan         - Scan all GPIO pins for capabilities       ║");
  Serial.println("║ restart      - Run full diagnostics again                ║");
  Serial.println("║ help         - Show this help message                    ║");
  Serial.println("║                                                           ║");
  Serial.println("║ In interactive mode:                                      ║");
  Serial.println("║ moisture     - Test moisture sensor                      ║");
  Serial.println("║ dht          - Test DHT22 sensor                         ║");
  Serial.println("║ relay        - Test relay module                         ║");
  Serial.println("║ pins         - Show pin recommendations                  ║");
  Serial.println("║ board        - Show board information                    ║");
  Serial.println("║ exit         - Exit interactive mode                     ║");
  Serial.println("╚═══════════════════════════════════════════════════════════╝");
  Serial.println();
}