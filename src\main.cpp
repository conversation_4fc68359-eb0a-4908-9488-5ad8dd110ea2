/*
 * ESP32 Soil Moisture Monitoring System
 * 
 * Components:
 * - Soil Moisture Sensor (Analog) on GPIO 32 (SAFE ADC pin)
 * - DHT22 Temperature/Humidity Sensor on GPIO 21 (SAFE digital I/O)
 * - Relay/LED Control on GPIO 18 (SAFE digital output) - HIGH triggers relay
 * 
 * Features:
 * - Reads soil moisture, temperature, and humidity
 * - Displays readings in Imperial (primary) and Metric units
 * - Automatic irrigation control via relay
 * - Error handling and sensor validation
 * - Modular code structure for easy expansion
 * 
 * Author: ESP32 Soil Moisture Project
 * Date: 2025
 */

#include <Arduino.h>

// ==================== DATA STRUCTURES ====================
// Structure to hold all sensor readings (DHT22 removed for now)
struct SensorReadings {
  // Soil Moisture
  int moisture_raw;
  float moisture_percent;

  // Status flags
  bool moisture_valid;
  bool system_healthy;
};

// ==================== FUNCTION PROTOTYPES ====================
void printWelcomeMessage();
void initializeHardware();
void testComponents();
void printTableHeader();
SensorReadings readAllSensors();
int readMoistureRaw();
float convertMoistureToPercent(int rawValue);
void displayReadings(SensorReadings readings);
String getMoistureDescription(float percent);
void controlIrrigation(SensorReadings readings);
void checkSystemHealth(SensorReadings readings);
void printCalibrationInfo();

// ==================== PIN DEFINITIONS ====================
// UPDATED SAFE PIN ASSIGNMENTS - Avoiding boot strap pins
#define MOISTURE_PIN 32     // Safe ADC pin for soil moisture sensor (GPIO32)
#define RELAY_PIN 18        // Safe digital output pin for relay control (GPIO18)

// DHT22 DISABLED FOR NOW - uncomment when sensor is working
// #define DHT_PIN 21          // Safe digital I/O pin for DHT22 sensor (GPIO21)

// OLD PROBLEMATIC PINS (commented out for reference):
// #define MOISTURE_PIN 2   // ❌ Boot strap pin - can cause boot issues
// #define DHT_PIN 15       // ❌ Boot strap pin - can cause boot issues
// #define RELAY_PIN 0      // ❌ Boot strap pin - MUST be HIGH to boot from flash

// ==================== SENSOR CONFIGURATION ====================
#define MOISTURE_SAMPLES 5  // Number of samples for moisture averaging
#define READING_INTERVAL 2500 // Reading interval in milliseconds (2.5 seconds)

// Soil moisture sensor calibration (based on actual sensor readings)
// Current readings: ~1200-1270 in moist soil
#define MOISTURE_DRY 1500   // Raw value when sensor is in dry soil (higher value)
#define MOISTURE_WET 1000   // Raw value when sensor is in wet soil (lower value)
#define MOISTURE_THRESHOLD 40.0 // Moisture percentage threshold for irrigation

// ==================== GLOBAL VARIABLES ====================
unsigned long lastReading = 0;
bool relayState = false;
int readingCount = 0;

// ==================== SETUP FUNCTION ====================
void setup() {
  Serial.begin(115200);
  delay(2000); // Allow serial monitor to connect
  
  printWelcomeMessage();
  
  // Initialize hardware
  initializeHardware();
  
  // Test all components
  testComponents();
  
  Serial.println("✓ System initialization complete!");
  Serial.println("Starting continuous monitoring...\n");
  
  printTableHeader();
}

// ==================== MAIN LOOP ====================
void loop() {
  unsigned long currentTime = millis();

  // Check if it's time for a new reading
  if (currentTime - lastReading >= READING_INTERVAL) {
    lastReading = currentTime;
    readingCount++;

    // Read all sensors
    SensorReadings readings = readAllSensors();

    // Display readings
    displayReadings(readings);

    // Control irrigation based on moisture level
    controlIrrigation(readings);

    // Check system health
    checkSystemHealth(readings);

    // Print calibration info every 50 readings
    if (readingCount % 50 == 0) {
      printCalibrationInfo();
    }
  }
}

// ==================== INITIALIZATION FUNCTIONS ====================
void initializeHardware() {
  Serial.println("Initializing hardware...");

  // Configure pins
  pinMode(RELAY_PIN, OUTPUT);          // Configure as output for relay control
  pinMode(MOISTURE_PIN, INPUT);
  digitalWrite(RELAY_PIN, LOW);        // Start with relay off

  Serial.println("✓ Hardware initialized (DHT22 disabled)");
}

void testComponents() {
  Serial.println("Testing components...");

  // Test relay/LED
  Serial.println("  Testing relay/LED...");
  for (int i = 0; i < 2; i++) {
    digitalWrite(RELAY_PIN, HIGH);
    delay(200);
    digitalWrite(RELAY_PIN, LOW);
    delay(200);
  }
  Serial.println("  ✓ Relay/LED test complete");

  // Test moisture sensor
  Serial.println("  Testing soil moisture sensor...");
  int testMoisture = analogRead(MOISTURE_PIN);
  Serial.printf("    Raw reading: %d\n", testMoisture);
  if (testMoisture > 0 && testMoisture < 4095) {
    Serial.println("  ✓ Soil moisture sensor responding");
  } else {
    Serial.println("  ⚠ Soil moisture sensor readings unusual - check connections");
  }

  Serial.println("  DHT22 sensor: DISABLED (sensor issue)");
  Serial.println("✓ Component testing complete\n");
}

// ==================== SENSOR READING FUNCTIONS ====================
SensorReadings readAllSensors() {
  SensorReadings readings;

  // Read soil moisture sensor with averaging
  readings.moisture_raw = readMoistureRaw();
  readings.moisture_valid = (readings.moisture_raw >= 0);

  if (readings.moisture_valid) {
    readings.moisture_percent = convertMoistureToPercent(readings.moisture_raw);
  } else {
    readings.moisture_percent = 0;
  }

  // Overall system health (only moisture sensor for now)
  readings.system_healthy = readings.moisture_valid;

  return readings;
}

int readMoistureRaw() {
  long sum = 0;
  int validReadings = 0;
  
  // Take multiple samples for accuracy
  for (int i = 0; i < MOISTURE_SAMPLES; i++) {
    int reading = analogRead(MOISTURE_PIN);
    if (reading >= 0 && reading <= 4095) {
      sum += reading;
      validReadings++;
    }
    delay(20); // Small delay between readings
  }
  
  if (validReadings == 0) {
    return -1; // Error indicator
  }
  
  return sum / validReadings;
}

float convertMoistureToPercent(int rawValue) {
  // Convert raw ADC value to percentage
  // For capacitive sensors: Higher raw values = drier soil (0% moisture)
  // Lower raw values = wetter soil (higher % moisture)
  float percent = map(rawValue, MOISTURE_DRY, MOISTURE_WET, 0, 100);

  // Constrain to valid range
  return constrain(percent, 0, 100);
}

// ==================== DISPLAY FUNCTIONS ====================
void printWelcomeMessage() {
  Serial.println("╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║           ESP32 Soil Moisture Monitoring System          ║");
  Serial.println("║                                                           ║");
  Serial.println("║  Components (UPDATED SAFE PINS):                         ║");
  Serial.println("║  • Soil Moisture Sensor (GPIO 32) - Safe ADC pin         ║");
  Serial.println("║  • Relay/LED Control (GPIO 18) - Safe digital output     ║");
  Serial.println("║  • DHT22 Temp/Humidity - DISABLED (sensor issue)         ║");
  Serial.println("║                                                           ║");
  Serial.println("║  Units: Imperial (Primary) / Metric (Secondary)          ║");
  Serial.println("╚═══════════════════════════════════════════════════════════╝");
  Serial.println();
}

void printTableHeader() {
  Serial.println("┌──────┬─────────────────┬────────┐");
  Serial.println("│ #    │ Soil Moisture   │ Relay  │");
  Serial.println("│      │ % (Raw)         │ Status │");
  Serial.println("├──────┼─────────────────┼────────┤");
}

void displayReadings(SensorReadings readings) {
  Serial.print("│ ");
  Serial.printf("%4d", readingCount);
  Serial.print(" │ ");

  // Soil moisture display
  if (readings.moisture_valid) {
    Serial.printf("%5.1f%% (%4d)", readings.moisture_percent, readings.moisture_raw);
  } else {
    Serial.print("   ERROR      ");
  }
  Serial.print(" │ ");

  // Relay status
  Serial.printf(" %s ", relayState ? " ON " : "OFF ");
  Serial.println(" │");

  // Add moisture level description every 10 readings
  if (readingCount % 10 == 0 && readings.moisture_valid) {
    Serial.println("├──────┴─────────────────┴────────┤");
    Serial.print("│ Soil Condition: ");
    Serial.print(getMoistureDescription(readings.moisture_percent));

    // Pad the line to fit the table width
    String desc = getMoistureDescription(readings.moisture_percent);
    for (int i = desc.length(); i < 16; i++) {
      Serial.print(" ");
    }
    Serial.println(" │");
    Serial.println("├──────┬─────────────────┬────────┤");
  }
}

String getMoistureDescription(float percent) {
  if (percent >= 80) return "Saturated - Too Wet";
  else if (percent >= 60) return "Very Moist - Good";
  else if (percent >= 40) return "Moist - Adequate";
  else if (percent >= 20) return "Dry - Needs Water";
  else return "Very Dry - Critical";
}

// ==================== CONTROL FUNCTIONS ====================
void controlIrrigation(SensorReadings readings) {
  if (!readings.moisture_valid) return;

  bool shouldActivate = readings.moisture_percent < MOISTURE_THRESHOLD;

  if (shouldActivate != relayState) {
    relayState = shouldActivate;
    digitalWrite(RELAY_PIN, relayState ? HIGH : LOW);

    Serial.println("├──────┴─────────────────┴────────┤");
    Serial.print("│ IRRIGATION ");
    Serial.print(relayState ? "ACTIVATED" : "DEACTIVATED");
    Serial.print(" - Moisture: ");
    Serial.print(readings.moisture_percent, 1);
    Serial.print("%");

    // Pad the line to fit new table width
    String msg = "IRRIGATION " + String(relayState ? "ACTIVATED" : "DEACTIVATED") + " - Moisture: " + String(readings.moisture_percent, 1) + "%";
    for (int i = msg.length(); i < 32; i++) {
      Serial.print(" ");
    }
    Serial.println(" │");
    Serial.println("├──────┬─────────────────┬────────┤");
  }
}

void checkSystemHealth(SensorReadings readings) {
  static int errorCount = 0;

  if (!readings.system_healthy) {
    errorCount++;
    if (errorCount >= 5) { // Alert after 5 consecutive errors
      Serial.println("├──────┴─────────────────┴────────┤");
      Serial.println("│ ⚠ SYSTEM ALERT: Moisture sensor error! │");
      Serial.println("│   Check sensor connections and power    │");
      Serial.println("├──────┬─────────────────┬────────┤");
      errorCount = 0; // Reset counter
    }
  } else {
    errorCount = 0; // Reset on successful reading
  }
}

// ==================== UTILITY FUNCTIONS ====================
void printCalibrationInfo() {
  Serial.println("\n╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║                    CALIBRATION INFO                      ║");
  Serial.println("╠═══════════════════════════════════════════════════════════╣");
  Serial.println("║ To calibrate your soil moisture sensor:                  ║");
  Serial.println("║                                                           ║");
  Serial.println("║ 1. Place sensor in completely DRY soil                   ║");
  Serial.println("║    Note the raw value and update MOISTURE_DRY            ║");
  Serial.println("║                                                           ║");
  Serial.println("║ 2. Place sensor in completely WET soil                   ║");
  Serial.println("║    Note the raw value and update MOISTURE_WET            ║");
  Serial.println("║                                                           ║");
  Serial.println("║ 3. Adjust MOISTURE_THRESHOLD for irrigation trigger      ║");
  Serial.println("║                                                           ║");
  Serial.printf("║ Current settings:                                         ║\n");
  Serial.printf("║   Dry value: %4d                                        ║\n", MOISTURE_DRY);
  Serial.printf("║   Wet value: %4d                                        ║\n", MOISTURE_WET);
  Serial.printf("║   Threshold: %4.1f%%                                      ║\n", MOISTURE_THRESHOLD);
  Serial.println("╚═══════════════════════════════════════════════════════════╝");
}
